import { useEffect } from 'react';

import { Outlet, createRootRoute, useNavigate, useLocation } from '@tanstack/react-router';

import AdminLayout from '../components/layout/AdminLayout';
import ProtectedRoute from '../components/auth/ProtectedRoute';
import { useAuth } from '@shared/hooks/useAuth';

function NotFound() {
    const navigate = useNavigate();
    const { isAuthenticated } = useAuth();

    useEffect(() => {
        if (isAuthenticated) {
            navigate({
                to: '/dashboard'
            });
        } else {
            navigate({
                to: '/login'
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated]);

    return null;
}

function Layout() {
    const location = useLocation();
    const isLoginPage = location.pathname === '/login';

    // Don't wrap login page with ProtectedRoute
    if (isLoginPage) {
        return <Outlet />;
    }

    return (
        <ProtectedRoute requiredRole="admin">
            <AdminLayout>
                <Outlet />
            </AdminLayout>
        </ProtectedRoute>
    );
}

export const Route = createRootRoute({
    component: Layout,
    notFoundComponent: NotFound
});
