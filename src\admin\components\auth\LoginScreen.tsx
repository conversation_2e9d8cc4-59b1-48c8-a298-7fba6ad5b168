import React, { useState } from 'react';

import {
    Analytics,
    BusinessCenter,
    CloudSync,
    Email as EmailIcon,
    LockOutlined as <PERSON><PERSON>con,
    Lock as PasswordIcon,
    Security,
    SmartToy,
    Visibility,
    VisibilityOff
} from '@mui/icons-material';
import {
    Alert,
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    Container,
    Divider,
    FormControlLabel,
    Grid,
    IconButton,
    InputAdornment,
    Link,
    Paper,
    Tab,
    Tabs,
    TextField,
    Typography
} from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';
import { useNavigate } from '@tanstack/react-router';

const LoginScreen: React.FC = () => {
    const navigate = useNavigate();
    const { login, loading, error } = useAuth();
    const [tabIndex, setTabIndex] = useState(0);

    const [formData, setFormData] = useState({
        email: '',
        password: '',
        rememberMe: false
    });

    const [showPassword, setShowPassword] = useState(false);
    const [formErrors, setFormErrors] = useState<{
        email?: string;
        password?: string;
    }>({});

    const validateForm = () => {
        const errors: { email?: string; password?: string } = {};

        if (!formData.email) {
            errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            errors.email = 'Email is invalid';
        }

        if (!formData.password) {
            errors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            errors.password = 'Password must be at least 6 characters';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const result = await login({
            email: formData.email,
            password: formData.password,
            rememberMe: formData.rememberMe
        });

        if (result.success) {
            navigate({ to: '/dashboard' });
        }
    };

    const handleInputChange =
        (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = field === 'rememberMe' ? e.target.checked : e.target.value;
            setFormData(prev => ({ ...prev, [field]: value }));

            // Clear field error when user starts typing
            if (formErrors[field as keyof typeof formErrors]) {
                setFormErrors(prev => ({ ...prev, [field]: undefined }));
            }
        };

    return (
        <Box
            sx={{
                minHeight: '50vh',
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                backgroundColor: '#f5f6fa'
            }}
        >
            {/* Left Panel - Promotional Content */}
            <Box
                sx={{
                    flex: 1,
                    minHeight: { xs: '50vh', md: '50vh' },
                    background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    p: { xs: 2, md: 4 },
                    position: 'relative',
                    overflow: 'hidden'
                }}
            >
                {/* Background decorative elements */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        opacity: 0.1,
                        background: `
                            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
                            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%),
                            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 0%, transparent 50%)
                        `
                    }}
                />

                <Box sx={{ textAlign: 'center', zIndex: 1 }}>
                    <Box sx={{ mb: 4 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'white' }}>
                            HOHO
                        </Typography>
                        <Typography variant="subtitle1" sx={{ opacity: 0.9, color: 'white' }}>
                            hehe
                        </Typography>
                    </Box>

                    <Typography
                        variant="h3"
                        sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center' }}
                    >
                        hjhj
                    </Typography>
                    <Typography
                        variant="h3"
                        sx={{ fontWeight: 'bold', mb: 4, textAlign: 'center' }}
                    >
                        haha
                    </Typography>

                    {/* AI Robot Icon */}
                    <Box
                        sx={{
                            width: 120,
                            height: 120,
                            borderRadius: '50%',
                            backgroundColor: 'rgba(255,255,255,0.2)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mx: 'auto',
                            mb: 4,
                            border: '3px solid rgba(255,255,255,0.3)'
                        }}
                    >
                        <SmartToy sx={{ fontSize: 60, color: 'white' }} />
                    </Box>

                    {/* Feature Icons */}
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mb: 4 }}>
                        <Box sx={{ textAlign: 'center' }}>
                            <BusinessCenter sx={{ fontSize: 32, mb: 1 }} />
                            <Typography variant="caption" display="block">
                                Quản lý
                            </Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center' }}>
                            <Analytics sx={{ fontSize: 32, mb: 1 }} />
                            <Typography variant="caption" display="block">
                                Phân tích
                            </Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center' }}>
                            <Security sx={{ fontSize: 32, mb: 1 }} />
                            <Typography variant="caption" display="block">
                                Bảo mật
                            </Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center' }}>
                            <CloudSync sx={{ fontSize: 32, mb: 1 }} />
                            <Typography variant="caption" display="block">
                                Đồng bộ
                            </Typography>
                        </Box>
                    </Box>
                </Box>
            </Box>

            {/* Right Panel - Login Form */}
            <Box
                sx={{
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: { xs: 2, md: 4 },
                    backgroundColor: 'white'
                }}
            >
                <Card sx={{ width: '50%', maxWidth: 420, borderRadius: 3, boxShadow: 4 }}>
                    <CardContent sx={{ p: 4 }}>
                        <Box sx={{ textAlign: 'center', mb: 3 }}>
                            <img src="/logo.png" alt="logo" height={40} />
                            <Typography variant="h6" sx={{ mt: 2 }}>
                                Đăng nhập
                            </Typography>
                        </Box>

                        <Tabs
                            value={tabIndex}
                            onChange={(e, newIndex) => setTabIndex(newIndex)}
                            centered
                        >
                            <Tab label="Với mật khẩu" />
                            <Tab label="Với mã QR" />
                        </Tabs>

                        {tabIndex === 0 ? (
                            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
                                {error && (
                                    <Alert severity="error" sx={{ mb: 2 }}>
                                        {error}
                                    </Alert>
                                )}
                                <TextField
                                    label="Số điện thoại/email"
                                    fullWidth
                                    margin="normal"
                                    value={formData.email}
                                    onChange={handleInputChange('email')}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <EmailIcon />
                                            </InputAdornment>
                                        )
                                    }}
                                />
                                <TextField
                                    label="Mật khẩu"
                                    fullWidth
                                    margin="normal"
                                    type={showPassword ? 'text' : 'password'}
                                    value={formData.password}
                                    onChange={handleInputChange('password')}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <PasswordIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() => setShowPassword(!showPassword)}
                                                >
                                                    {showPassword ? (
                                                        <VisibilityOff />
                                                    ) : (
                                                        <Visibility />
                                                    )}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={formData.rememberMe}
                                            onChange={handleInputChange('rememberMe')}
                                        />
                                    }
                                    label="Ghi nhớ đăng nhập"
                                    sx={{ mt: 1 }}
                                />
                                <Button
                                    type="submit"
                                    fullWidth
                                    variant="contained"
                                    color="primary"
                                    disabled={loading}
                                    sx={{ mt: 3, mb: 2, py: 1.5, fontWeight: 600 }}
                                >
                                    {loading ? (
                                        <CircularProgress size={24} color="inherit" />
                                    ) : (
                                        'Đăng nhập'
                                    )}
                                </Button>

                                <Box
                                    sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}
                                >
                                    <Link href="#" underline="hover">
                                        Quên mật khẩu?
                                    </Link>
                                    <Link href="#" underline="hover">
                                        Đăng ký
                                    </Link>
                                </Box>
                                <Box sx={{ mt: 3, textAlign: 'center' }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Demo Credentials:
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Admin: <EMAIL> / admin123
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        User: <EMAIL> / user123
                                    </Typography>
                                </Box>
                            </Box>
                        ) : (
                            <Box sx={{ textAlign: 'center', mt: 4 }}>
                                <Typography variant="body2" color="text.secondary">
                                    Quét mã QR bằng ứng dụng MISA để đăng nhập
                                </Typography>
                                <Box
                                    sx={{
                                        width: 150,
                                        height: 150,
                                        mx: 'auto',
                                        mt: 2,
                                        border: '1px solid #ccc',
                                        borderRadius: 2,
                                        background: '#eee'
                                    }}
                                />
                            </Box>
                        )}

                        <Divider sx={{ my: 3 }}>Hoặc đăng nhập với</Divider>

                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                            <IconButton>
                                <img src="/google-icon.svg" alt="Google" width={24} />
                            </IconButton>
                            <IconButton>
                                <img src="/apple-icon.svg" alt="Apple" width={24} />
                            </IconButton>
                            <IconButton>
                                <img src="/microsoft-icon.svg" alt="Microsoft" width={24} />
                            </IconButton>
                        </Box>
                    </CardContent>
                </Card>
            </Box>
        </Box>
    );
};
export default LoginScreen;
